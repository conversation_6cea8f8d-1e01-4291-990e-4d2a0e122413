#!/usr/bin/env node

const http = require('http');

// Test data for unified export
const testData = {
  images: [
    { filename: 'frame1.png' },
    { filename: 'frame2.png' },
    { filename: 'frame3.png' }
  ],
  transitions: [
    { type: 'fade', duration: 0.5 },
    { type: 'fade', duration: 0.5 }
  ],
  frameDurations: [1000, 1000, 1000],
  sessionId: 'test_session_' + Date.now(),
  fps: 30,
  quality: 'standard',
  resolution: '1080p'
};

console.log('🧪 Testing unified export endpoint...');
console.log('📤 Test data:', JSON.stringify(testData, null, 2));

// Test MP4 export
function testExport(format) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(testData);
    
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: `/api/unified-export/${format}`,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    console.log(`\n🎬 Testing ${format.toUpperCase()} export...`);
    
    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`📥 Response status: ${res.statusCode}`);
        console.log(`📥 Response headers:`, res.headers);
        
        try {
          const response = JSON.parse(data);
          console.log(`📥 Response body:`, JSON.stringify(response, null, 2));
          
          if (response.success) {
            console.log(`✅ ${format.toUpperCase()} export successful!`);
            if (response.jobId) {
              console.log(`🔍 Job ID: ${response.jobId}`);
              console.log(`📊 Status URL: ${response.statusUrl}`);
              console.log(`⬇️ Download URL: ${response.downloadUrl}`);
              
              // Test status endpoint
              setTimeout(() => {
                testStatus(response.jobId, format);
              }, 1000);
            }
          } else {
            console.log(`❌ ${format.toUpperCase()} export failed:`, response.error);
          }
          
          resolve(response);
        } catch (error) {
          console.log(`❌ Failed to parse response:`, error.message);
          console.log(`📥 Raw response:`, data);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.log(`❌ Request error:`, error.message);
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

function testStatus(jobId, format) {
  const options = {
    hostname: 'localhost',
    port: 3001,
    path: `/api/export/status/${jobId}`,
    method: 'GET'
  };

  console.log(`\n🔍 Testing status for ${format.toUpperCase()} job: ${jobId}`);
  
  const req = http.request(options, (res) => {
    let data = '';
    
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log(`📊 Status response: ${res.statusCode}`);
      
      try {
        const response = JSON.parse(data);
        console.log(`📊 Status data:`, JSON.stringify(response, null, 2));
        
        if (response.success) {
          console.log(`✅ Status check successful for ${format.toUpperCase()}!`);
        } else {
          console.log(`❌ Status check failed for ${format.toUpperCase()}:`, response.error);
        }
      } catch (error) {
        console.log(`❌ Failed to parse status response:`, error.message);
        console.log(`📊 Raw status response:`, data);
      }
    });
  });

  req.on('error', (error) => {
    console.log(`❌ Status request error:`, error.message);
  });

  req.end();
}

// Run tests
async function runTests() {
  try {
    console.log('🚀 Starting unified export tests...\n');
    
    // Test MP4 first
    await testExport('mp4');
    
    // Wait a bit before next test
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Test GIF
    await testExport('gif');
    
    console.log('\n🏁 Tests completed!');
  } catch (error) {
    console.error('💥 Test failed:', error);
  }
}

runTests();
